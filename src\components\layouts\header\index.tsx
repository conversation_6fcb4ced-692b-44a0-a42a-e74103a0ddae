import { useAuth } from '@/contexts/auth';
import { LogoLink } from '@/pages/App/components/MenuSideBar/components';
import { ChevronDown, Menu } from 'lucide-react';
import { useEffect, useState } from 'react';
import { Avatar, AvatarFallback, AvatarImage } from '../../ui/avatar';
import { Button } from '../../ui/button';
import { ProfileChange } from './profileChange';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { useNavigate } from 'react-router-dom';

export function Header() {
  const navigate = useNavigate();
  const { user, logout } = useAuth();
  const [hasToShowProfileChange, setHasToShowProfileChange] = useState(false);

  const handleSignOut = () => {
    logout();
  };

  useEffect(() => {
    const isAdmin = user?.roles.some((role) => role.name.includes('Admin'));
    const hasMoreThanOneCompany = user?.companies.length > 1;
    const hasMoreThanOneRole = user?.roles.length > 1;

    if (isAdmin || hasMoreThanOneCompany || hasMoreThanOneRole) {
      setHasToShowProfileChange(true);
    } else {
      setHasToShowProfileChange(false);
    }
  }, [user]);

  return (
    <div className="flex items-center justify-between h-20 w-full p-6 shadow-sm">
      <div className="flex items-center gap-6">
        <Button
          size="icon"
          variant="ghost"
        >
          <Menu />
        </Button>
        <LogoLink />
      </div>

      {hasToShowProfileChange && <ProfileChange />}

      <DropdownMenu>
        <DropdownMenuTrigger>
          <Button
            variant="ghost"
            className="flex items-center gap-2"
          >
            <Avatar className="w-8.5 h-8.5">
              <AvatarImage />
              <AvatarFallback title={user.fullName}>
                {user.fullName[0]}
              </AvatarFallback>
            </Avatar>
            <ChevronDown />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent className="w-58">
          <DropdownMenuLabel>{user.email ?? ''}</DropdownMenuLabel>
          <DropdownMenuSeparator />
          <DropdownMenuItem onClick={() => navigate('/app/perfil')}>
            Perfil
          </DropdownMenuItem>
          <DropdownMenuItem
            variant="destructive"
            onClick={handleSignOut}
          >
            Sair
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
}
