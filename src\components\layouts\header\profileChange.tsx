import { Button } from '@/components/ui/button';
import { Combobox } from '@/components/ui/combobox';
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { SelectField } from '@/components/ui/select';
import { useApi } from '@/contexts/api';
import { useAuth } from '@/contexts/auth';
import { zodResolver } from '@hookform/resolvers/zod';
import { useQuery } from '@tanstack/react-query';
import { ChevronDown } from 'lucide-react';
import { Controller, useForm } from 'react-hook-form';
import { z } from 'zod';

const profileValidation = z.object({
  profile: z.string().min(1, 'Campo obrigatório'),
  company: z.string().min(1, 'Campo obrigatório'),
});

type ProfileValidationType = z.infer<typeof profileValidation>;

export function ProfileChange() {
  const { user } = useAuth();

  const {
    clients: { loadClientCompanies, loadClientCompany, updateUserRole },
  } = useApi();

  const {
    control,
    handleSubmit,
    formState: { errors },
  } = useForm<ProfileValidationType>({
    resolver: zodResolver(profileValidation),
  });

  const { data: allCompaniesData, isLoading: isLoadingCompanies } = useQuery({
    queryKey: ['allCompanies'],
    queryFn: () => loadClientCompanies(),
    refetchOnWindowFocus: false,
  });

  const handleSubmitForm = (data: ProfileValidationType) => {
    console.log(data);
  };

  console.log(allCompaniesData);

  return (
    <Dialog>
      <form onSubmit={handleSubmit(handleSubmitForm)}>
        <DialogTrigger asChild>
          <Button
            variant="outline"
            className="rounded-lg"
          >
            {user.currentRole.name}{' '}
            {user.currentRole.requiresCompany && ` - ${user.companyName ?? ''}`}
            <ChevronDown />
          </Button>
        </DialogTrigger>
        <DialogContent className="sm:max-w-[480px] p-8">
          <DialogHeader className="flex flex-col items-center mb-8">
            <DialogTitle>
              <span className="text-2xl">Trocar perfil/empresa</span>
            </DialogTitle>
          </DialogHeader>
          <div className="flex flex-col gap-10">
            <Controller
              name="profile"
              control={control}
              render={({ field }) => (
                <SelectField
                  label="Perfil"
                  placeholder="Selecione um perfil"
                  options={[]}
                  onValueChange={field.onChange}
                  errorMessage={errors.profile?.message}
                />
              )}
            />
            <Combobox />

            <div className="flex w-full justify-between gap-8">
              <DialogClose asChild>
                <Button
                  variant="outline"
                  className="flex-1 h-10"
                >
                  Cancelar
                </Button>
              </DialogClose>
              <Button
                type="submit"
                className="flex-1 h-10"
              >
                Salvar
              </Button>
            </div>
          </div>
        </DialogContent>
      </form>
    </Dialog>
  );
}
